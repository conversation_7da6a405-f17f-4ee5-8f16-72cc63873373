#!/usr/bin/env python3
"""
测试文档上传API的脚本
"""

import requests
import os

def test_upload_api():
    """测试文档上传API"""
    base_url = "http://localhost:8000"
    
    # 测试文件上传
    print("=== 测试文档上传 ===")
    
    test_file_path = "test_upload.txt"
    
    if not os.path.exists(test_file_path):
        print(f"❌ 测试文件不存在: {test_file_path}")
        return
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'files': (test_file_path, f, 'text/plain')}
            response = requests.post(f"{base_url}/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文档上传成功:")
            print(f"   消息: {result.get('message')}")
            print(f"   处理文件数: {result.get('file_count')}")
            print(f"   总上传数: {result.get('total_uploaded')}")
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 上传请求异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 检查文件是否保存到documents目录
    print("=== 检查文件保存 ===")
    documents_dir = "data/documents"
    uploaded_file_path = os.path.join(documents_dir, test_file_path)
    
    if os.path.exists(uploaded_file_path):
        print(f"✅ 文件已保存到: {uploaded_file_path}")
        with open(uploaded_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"   文件内容预览: {content[:100]}...")
    else:
        print(f"❌ 文件未保存到: {uploaded_file_path}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试搜索新上传的文档
    print("=== 测试搜索新文档 ===")
    
    search_data = {
        "query": "测试上传",
        "limit": 3,
        "mode": "hybrid"
    }
    
    try:
        response = requests.post(
            f"{base_url}/search",
            headers={"Content-Type": "application/json"},
            json=search_data
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 搜索成功，找到 {results.get('total', 0)} 个结果:")
            
            for i, result in enumerate(results.get('results', []), 1):
                print(f"   结果 {i}:")
                print(f"     评分: {result.get('score', 0):.4f}")
                print(f"     来源: {result.get('source', 'unknown')}")
                text = result.get('text', '')
                preview = text[:100] + "..." if len(text) > 100 else text
                print(f"     内容: {preview}")
                print()
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 搜索请求异常: {e}")

if __name__ == "__main__":
    test_upload_api()
