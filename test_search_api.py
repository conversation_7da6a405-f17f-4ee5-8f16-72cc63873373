#!/usr/bin/env python3
"""
测试搜索API的脚本
"""

import requests
import json

def test_search_api():
    """测试搜索API"""
    base_url = "http://localhost:8000"
    
    # 测试统计信息
    print("=== 测试统计信息 ===")
    try:
        response = requests.get(f"{base_url}/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 统计信息获取成功:")
            print(f"   集合名称: {stats.get('collection_name')}")
            print(f"   文档节点数: {stats.get('points_count')}")
            print(f"   词汇表大小: {stats.get('vocab_size')}")
            print(f"   状态: {stats.get('status')}")
            print(f"   BM25可用: {stats.get('bm25_available')}")
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 统计信息请求异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试搜索功能
    test_queries = [
        {"query": "人工智能", "mode": "hybrid"},
        {"query": "机器学习", "mode": "hybrid"},
        {"query": "深度学习", "mode": "dense"},
        {"query": "自然语言处理", "mode": "sparse"},
        {"query": "计算机视觉", "mode": "bm25"},
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"=== 测试搜索 {i}: {test_case['query']} ({test_case['mode']}模式) ===")
        
        search_data = {
            "query": test_case["query"],
            "limit": 3,
            "mode": test_case["mode"],
            "alpha": 0.5
        }
        
        try:
            response = requests.post(
                f"{base_url}/search",
                headers={"Content-Type": "application/json"},
                json=search_data
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ 搜索成功，找到 {results.get('total', 0)} 个结果:")
                
                for j, result in enumerate(results.get('results', []), 1):
                    print(f"   结果 {j}:")
                    print(f"     评分: {result.get('score', 0):.4f}")
                    print(f"     来源: {result.get('source', 'unknown')}")
                    print(f"     模式: {result.get('mode', 'unknown')}")
                    text = result.get('text', '')
                    preview = text[:100] + "..." if len(text) > 100 else text
                    print(f"     内容: {preview}")
                    print()
            else:
                print(f"❌ 搜索失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"❌ 搜索请求异常: {e}")
        
        print("-" * 50 + "\n")

if __name__ == "__main__":
    test_search_api()
