<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="modeSelect" class="form-label">搜索模式:</label>
              <select class="form-select" id="modeSelect">
                <option value="hybrid">
                  🔥 混合搜索 (推荐) - 结合关键词和语义
                </option>
                <option value="dense">🧠 语义搜索 - 理解文本含义</option>
                <option value="bm25">⚡ 关键词搜索 - 快速精确匹配</option>
              </select>
            </div>
            <div class="mb-3" id="alphaControl" style="display: none">
              <label for="alphaRange" class="form-label"
                >稀疏向量权重: <span id="alphaValue">0.5</span></label
              >
              <input
                type="range"
                class="form-range"
                id="alphaRange"
                min="0"
                max="1"
                step="0.1"
                value="0.5"
              />
              <small class="text-muted">0.0=纯密向量, 1.0=纯稀疏向量</small>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>混合搜索</h5>
                    <p>BM25 + 向量搜索的最佳组合</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>Qdrant 存储</h5>
                    <p>高性能向量数据库</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
  </body>
</html>
