from pydantic import BaseModel
from typing import List, Optional

class DocumentUpload(BaseModel):
    filename: str
    content: str

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5
    mode: Optional[str] = "hybrid"  # "hybrid", "dense", "bm25"
    alpha: Optional[float] = 0.5  # 混合搜索中稀疏向量权重

class SearchResult(BaseModel):
    text: str
    score: float
    source: str
    mode: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
    mode: str
    alpha: Optional[float] = None

class StatsResponse(BaseModel):
    collection_name: str
    points_count: int
    vectors_count: Optional[int] = None
    vocab_size: int
    status: str

class UploadResponse(BaseModel):
    message: str
    file_count: int
    total_uploaded: int

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str
